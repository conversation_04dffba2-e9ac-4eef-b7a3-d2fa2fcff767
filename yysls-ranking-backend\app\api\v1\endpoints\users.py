"""
用户管理API端点

提供用户信息查询、更新等功能
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, require_admin, require_user_or_admin
from app.services.user_service import UserService
from app.schemas.user import UserUpdate, UserResponse, UserCreate
from app.schemas.common import ResponseModel, PaginatedResponse
from app.models.user import UserRole

router = APIRouter()
user_service = UserService()


@router.get("/me", response_model=ResponseModel[UserResponse], summary="获取当前用户信息")
async def get_current_user_info(
    current_user = Depends(get_current_user)
) -> ResponseModel[UserResponse]:
    """
    获取当前登录用户的详细信息
    """
    return ResponseModel(
        code=200,
        message="获取用户信息成功",
        data=UserResponse.from_orm(current_user)
    )


@router.put("/me", response_model=ResponseModel[UserResponse], summary="更新当前用户信息")
async def update_current_user(
    user_data: UserUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[UserResponse]:
    """
    更新当前登录用户的信息
    """
    try:
        # 更新用户信息
        updated_user = user_service.update(db, db_obj=current_user, obj_in=user_data)
        
        return ResponseModel(
            code=200,
            message="更新用户信息成功",
            data=UserResponse.from_orm(updated_user)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户信息失败: {str(e)}"
        )


@router.get("", response_model=ResponseModel[PaginatedResponse[UserResponse]], summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    role: Optional[UserRole] = Query(None, description="用户角色筛选"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（用户名、昵称）"),
    admin_user = Depends(require_admin),
    db: Session = Depends(get_db)
) -> ResponseModel[PaginatedResponse[UserResponse]]:
    """
    获取用户列表（需要管理员权限）

    - **page**: 页码，从1开始
    - **size**: 每页大小，最大100
    - **role**: 用户角色筛选（可选）
    - **is_active**: 激活状态筛选（可选）
    - **search**: 搜索关键词（可选）
    """
    
    try:
        # 构建筛选条件
        filters = {}
        if role:
            filters["role"] = role
        if is_active is not None:
            filters["is_active"] = is_active
        
        # 获取用户列表
        if search:
            users, total = user_service.search_users(
                db, search_term=search, skip=(page - 1) * size, limit=size, **filters
            )
        else:
            users, total = user_service.get_multi_with_total(
                db, skip=(page - 1) * size, limit=size, **filters
            )
        
        # 转换为响应模型
        user_responses = [UserResponse.from_orm(user) for user in users]
        
        # 构建分页响应
        pages = (total + size - 1) // size
        paginated_data = PaginatedResponse(
            items=user_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return ResponseModel(
            code=200,
            message="获取用户列表成功",
            data=paginated_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )


@router.get("/{user_id}", response_model=ResponseModel[UserResponse], summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[UserResponse]:
    """
    获取指定用户的详细信息
    
    普通用户只能查看自己的信息，管理员可以查看所有用户信息
    """
    # 权限检查（管理员、超级管理员或本人）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN] and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        user = user_service.get(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return ResponseModel(
            code=200,
            message="获取用户详情成功",
            data=UserResponse.from_orm(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户详情失败: {str(e)}"
        )


@router.put("", response_model=ResponseModel[UserResponse], summary="更新用户信息")
async def update_user(
    user_data: UserUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[UserResponse]:
    """
    更新用户信息
    
    普通用户只能更新自己的信息，管理员可以更新所有用户信息
    """
    # 权限检查（管理员、超级管理员或本人）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN] and current_user.id != user_data.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查用户是否存在
        existing_user = user_service.get(db, user_data.id)
        if not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 普通用户不能修改角色和激活状态
        if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
            user_data.role = None
            user_data.is_active = None

        # 更新用户信息
        updated_user = user_service.update(db, db_obj=existing_user, obj_in=user_data)
        
        return ResponseModel(
            code=200,
            message="更新用户信息成功",
            data=UserResponse.from_orm(updated_user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户信息失败: {str(e)}"
        )


@router.post("", response_model=ResponseModel[UserResponse], summary="创建用户")
async def create_user(
    user_data: UserCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[UserResponse]:
    """
    创建新用户（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查用户名是否已存在
        existing_user = user_service.get_by_username(db, user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 创建用户
        user = user_service.create(db, obj_in=user_data)
        
        return ResponseModel(
            code=200,
            message="创建用户成功",
            data=UserResponse.from_orm(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}"
        )


@router.delete("/{user_id}", response_model=ResponseModel[None], summary="删除用户")
async def delete_user(
    user_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除用户（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    # 不能删除自己
    if current_user.id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )
    
    try:
        # 检查用户是否存在
        user = user_service.get(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 删除用户
        user_service.remove(db, id=user_id)
        
        return ResponseModel(
            code=200,
            message="删除用户成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除用户失败: {str(e)}"
        )
