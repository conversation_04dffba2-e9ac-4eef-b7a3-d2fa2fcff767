"""
用户编号生成器测试
"""
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.utils.user_number_generator import (
    UserNumberGenerator,
    generate_user_number,
    validate_user_number
)
from app.models.user import User


class TestUserNumberGenerator:
    """用户编号生成器测试类"""
    
    def test_validate_user_number_valid_formats(self):
        """测试有效的用户编号格式"""
        valid_numbers = [
            "YY00001",
            "YY00010",
            "YY12345",
            "YY99999"
        ]
        
        for number in valid_numbers:
            assert validate_user_number(number), f"应该验证通过: {number}"
    
    def test_validate_user_number_invalid_formats(self):
        """测试无效的用户编号格式"""
        invalid_numbers = [
            "",
            None,
            "YY0001",      # 少于5位数字
            "YY000001",    # 多于5位数字
            "XX00001",     # 错误的前缀
            "YY0000a",     # 包含字母
            "YY 0001",     # 包含空格
            "yy00001",     # 小写前缀
            "00001",       # 没有前缀
        ]
        
        for number in invalid_numbers:
            assert not validate_user_number(number), f"应该验证失败: {number}"
    
    def test_extract_number_from_user_number(self):
        """测试从用户编号中提取数字部分"""
        test_cases = [
            ("YY00001", 1),
            ("YY00010", 10),
            ("YY12345", 12345),
            ("YY99999", 99999),
            ("invalid", None),
            ("", None),
            (None, None)
        ]
        
        for user_number, expected in test_cases:
            result = UserNumberGenerator.extract_number_from_user_number(user_number)
            assert result == expected, f"用户编号 {user_number} 应该提取出 {expected}，实际得到 {result}"
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock(spec=Session)
        return session
    
    def test_get_max_user_number_empty_database(self, mock_db_session):
        """测试空数据库时获取最大用户编号"""
        # 模拟数据库返回0（没有用户）
        mock_db_session.execute.return_value.scalar.return_value = 0
        
        result = UserNumberGenerator._get_max_user_number(mock_db_session)
        assert result == 0
    
    def test_get_max_user_number_with_existing_users(self, mock_db_session):
        """测试有现有用户时获取最大用户编号"""
        # 模拟数据库返回最大编号123
        mock_db_session.execute.return_value.scalar.return_value = 123
        
        result = UserNumberGenerator._get_max_user_number(mock_db_session)
        assert result == 123
    
    def test_get_max_user_number_database_error(self, mock_db_session):
        """测试数据库查询错误时的处理"""
        # 模拟数据库查询异常
        mock_db_session.execute.side_effect = Exception("Database error")
        
        result = UserNumberGenerator._get_max_user_number(mock_db_session)
        assert result == 0  # 应该返回0作为默认值
    
    def test_generate_user_number_success(self, mock_db_session):
        """测试成功生成用户编号"""
        # 模拟获取最大编号为5
        with patch.object(UserNumberGenerator, '_get_max_user_number', return_value=5):
            # 模拟检查编号不存在
            mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
            
            result = UserNumberGenerator.generate_user_number(mock_db_session)
            assert result == "YY00006"
    
    def test_generate_user_number_with_conflict_retry(self, mock_db_session):
        """测试编号冲突时的重试机制"""
        # 模拟获取最大编号
        with patch.object(UserNumberGenerator, '_get_max_user_number', side_effect=[5, 6]):
            # 第一次检查编号已存在，第二次不存在
            mock_db_session.execute.return_value.scalar_one_or_none.side_effect = [
                Mock(),  # 第一次返回存在的用户（冲突）
                None     # 第二次返回None（不冲突）
            ]
            
            result = UserNumberGenerator.generate_user_number(mock_db_session)
            assert result == "YY00007"
    
    def test_generate_user_number_max_retries_exceeded(self, mock_db_session):
        """测试超过最大重试次数时抛出异常"""
        # 模拟获取最大编号总是返回相同值
        with patch.object(UserNumberGenerator, '_get_max_user_number', return_value=5):
            # 模拟编号总是冲突
            mock_db_session.execute.return_value.scalar_one_or_none.return_value = Mock()
            
            with pytest.raises(RuntimeError, match="生成用户编号失败，超过最大重试次数"):
                UserNumberGenerator.generate_user_number(mock_db_session)
    
    def test_generate_user_number_database_error(self, mock_db_session):
        """测试数据库错误时的处理"""
        # 模拟数据库查询异常
        mock_db_session.execute.side_effect = Exception("Database connection error")
        
        with pytest.raises(RuntimeError, match="生成用户编号失败，已重试"):
            UserNumberGenerator.generate_user_number(mock_db_session)
    
    def test_generate_user_number_convenience_function(self, mock_db_session):
        """测试便捷函数"""
        with patch.object(UserNumberGenerator, 'generate_user_number', return_value="YY00001") as mock_generate:
            result = generate_user_number(mock_db_session)
            assert result == "YY00001"
            mock_generate.assert_called_once_with(mock_db_session)


class TestUserNumberGeneratorIntegration:
    """用户编号生成器集成测试"""
    
    def test_number_format_consistency(self):
        """测试编号格式的一致性"""
        # 测试不同数字的格式化
        test_cases = [
            (1, "YY00001"),
            (10, "YY00010"),
            (100, "YY00100"),
            (1000, "YY01000"),
            (99999, "YY99999")
        ]
        
        for number, expected in test_cases:
            formatted = f"{UserNumberGenerator.PREFIX}{number:0{UserNumberGenerator.NUMBER_LENGTH}d}"
            assert formatted == expected
            assert validate_user_number(formatted)
    
    def test_prefix_and_length_constants(self):
        """测试前缀和长度常量"""
        assert UserNumberGenerator.PREFIX == "YY"
        assert UserNumberGenerator.NUMBER_LENGTH == 5
        assert UserNumberGenerator.MAX_RETRIES == 10
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试最小和最大可能的编号
        min_number = "YY00001"
        max_number = "YY99999"
        
        assert validate_user_number(min_number)
        assert validate_user_number(max_number)
        
        assert UserNumberGenerator.extract_number_from_user_number(min_number) == 1
        assert UserNumberGenerator.extract_number_from_user_number(max_number) == 99999
