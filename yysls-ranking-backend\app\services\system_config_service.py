"""
系统配置业务服务

处理系统配置相关的业务逻辑，包括：
- 配置项管理
- 配置值验证
- 配置缓存
- 配置分组管理
"""
from typing import Optional, List, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, func, distinct
import json

from app.services.base import BaseService
from app.models.system_config import SystemConfig


class SystemConfigService(BaseService):
    """系统配置服务类"""
    
    def __init__(self):
        super().__init__(SystemConfig)
        self._config_cache = {}  # 简单的内存缓存
    
    async def get_by_key(self, db: AsyncSession, config_key: str) -> Optional[SystemConfig]:
        """根据配置键获取配置（异步版本）"""
        try:
            result = await db.execute(
                select(SystemConfig).where(SystemConfig.config_key == config_key)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"根据键获取配置失败 key={config_key}: {str(e)}")
            raise

    def get_by_key_sync(self, db: Session, config_key: str) -> Optional[SystemConfig]:
        """根据配置键获取配置（同步版本）"""
        try:
            result = db.execute(
                select(SystemConfig).where(SystemConfig.config_key == config_key)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"根据键获取配置失败 key={config_key}: {str(e)}")
            raise
    
    async def get_config_value(
        self,
        db: AsyncSession,
        config_key: str,
        default_value: Any = None
    ) -> Any:
        """获取配置值（带类型转换）"""
        try:

            config = await self.get_by_key(db, config_key)
            if not config:
                return default_value

            # 根据值类型进行转换
            value = self._convert_config_value(config.config_value, config.config_type)

            # 缓存结果
            self._config_cache[config_key] = value

            return value
        except Exception as e:
            self.logger.error(f"获取配置值失败 key={config_key}: {str(e)}")
            return default_value
    
    async def set_config_value(
        self,
        db: AsyncSession,
        config_key: str,
        config_value: Any
    ) -> SystemConfig:
        """设置配置值"""
        try:
            config = await self.get_by_key(db, config_key)
            if not config:
                raise ValueError(f"配置项不存在 key={config_key}")

            # 转换为字符串存储
            config.config_value = str(config_value)

            await db.commit()
            await db.refresh(config)

            # 清除缓存
            if config_key in self._config_cache:
                del self._config_cache[config_key]

            self.logger.info(f"设置配置值成功 key={config_key}")
            return config
        except Exception as e:
            await db.rollback()
            self.logger.error(f"设置配置值失败 key={config_key}: {str(e)}")
            raise
    
    async def get_by_type(self, db: AsyncSession, config_type: str) -> List[SystemConfig]:
        """根据配置类型获取配置列表"""
        try:
            result = await db.execute(
                select(SystemConfig)
                .where(SystemConfig.config_type == config_type)
                .order_by(SystemConfig.config_key)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"根据类型获取配置失败 type={config_type}: {str(e)}")
            raise
    

    
    def get_public_configs(self, db: Session) -> Dict[str, Any]:
        """获取公开配置（用于前端）"""
        try:
            result = db.execute(
                select(SystemConfig)
                .where(SystemConfig.is_public == True)
            )
            configs = result.scalars().all()

            public_configs = {}
            for config in configs:
                value = self._convert_config_value(config.config_value, config.config_type)
                public_configs[config.config_key] = value

            return public_configs
        except Exception as e:
            self.logger.error(f"获取公开配置失败: {str(e)}")
            raise
    
    async def create_config(
        self, 
        db: AsyncSession, 
        config_data: Dict[str, Any]
    ) -> SystemConfig:
        """创建配置项"""
        try:
            # 检查配置键是否已存在
            existing = await self.get_by_key(db, config_data['config_key'])
            if existing:
                raise ValueError(f"配置键已存在 key={config_data['config_key']}")
            
            db_config = SystemConfig(**config_data)
            db.add(db_config)
            await db.commit()
            await db.refresh(db_config)
            
            self.logger.info(f"创建配置项成功 key={config_data['config_key']}")
            return db_config
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建配置项失败: {str(e)}")
            raise
    
    def _convert_config_value(self, value: str, value_type: str) -> Any:
        """转换配置值类型"""
        if not value:
            return None
        
        try:
            if value_type == 'boolean':
                return value.lower() in ('true', '1', 'yes', 'on')
            elif value_type == 'integer':
                return int(value)
            elif value_type == 'float':
                return float(value)
            elif value_type == 'json':
                return json.loads(value)
            else:  # string
                return value
        except (ValueError, json.JSONDecodeError) as e:
            self.logger.warning(f"配置值类型转换失败 value={value}, type={value_type}: {str(e)}")
            return value
    
    def _validate_config_value(self, value: Any, value_type: str) -> bool:
        """验证配置值"""
        # 基础类型验证
        try:
            if value_type == 'boolean':
                bool(value)
            elif value_type == 'integer':
                int(value)
            elif value_type == 'float':
                float(value)
            elif value_type == 'json':
                if isinstance(value, str):
                    json.loads(value)
        except (ValueError, json.JSONDecodeError):
            return False
        
        # TODO: 实现更复杂的验证规则
        return True
    
    def clear_cache(self):
        """清除配置缓存"""
        self._config_cache.clear()
        self.logger.info("配置缓存已清除")

    async def search_configs(
        self,
        db: AsyncSession,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        **filters
    ) -> tuple[List[SystemConfig], int]:
        """搜索配置（返回结果和总数）"""
        try:
            # 构建搜索条件
            search_conditions = or_(
                SystemConfig.config_key.ilike(f"%{search_term}%"),
                SystemConfig.description.ilike(f"%{search_term}%")
            )

            # 构建过滤条件
            filter_conditions = self._build_filters(**filters)

            # 组合所有条件
            all_conditions = [search_conditions]
            if filter_conditions:
                all_conditions.extend(filter_conditions)

            # 构建查询
            query = select(SystemConfig).where(and_(*all_conditions))
            count_query = select(func.count(SystemConfig.id)).where(and_(*all_conditions))

            # 获取总数
            total_result = await db.execute(count_query)
            total = total_result.scalar()

            # 获取数据
            query = query.order_by(SystemConfig.config_type, SystemConfig.config_key).offset(skip).limit(limit)
            result = await db.execute(query)
            configs = result.scalars().all()

            return configs, total
        except Exception as e:
            self.logger.error(f"搜索配置失败 search_term={search_term}: {str(e)}")
            raise

    def get_categories(self, db: Session) -> List[str]:
        """获取所有配置分类"""
        try:
            result = db.execute(
                select(distinct(SystemConfig.config_type))
                .where(SystemConfig.config_type.isnot(None))
                .order_by(SystemConfig.config_type)
            )
            return [row[0] for row in result.fetchall()]
        except Exception as e:
            self.logger.error(f"获取配置分类失败: {str(e)}")
            raise

    def get_multi_with_total(self, db: Session, skip: int = 0, limit: int = 100, **filters) -> tuple[List[SystemConfig], int]:
        """获取配置列表和总数"""
        try:
            # 构建查询条件
            query = select(SystemConfig)
            count_query = select(func.count(SystemConfig.id))

            # 应用过滤条件
            if filters:
                conditions = []
                for key, value in filters.items():
                    if hasattr(SystemConfig, key):
                        conditions.append(getattr(SystemConfig, key) == value)

                if conditions:
                    query = query.where(and_(*conditions))
                    count_query = count_query.where(and_(*conditions))

            # 获取总数
            total_result = db.execute(count_query)
            total = total_result.scalar()

            # 获取数据
            query = query.order_by(SystemConfig.config_type, SystemConfig.config_key).offset(skip).limit(limit)
            result = db.execute(query)
            configs = result.scalars().all()

            return configs, total
        except Exception as e:
            self.logger.error(f"获取配置列表失败: {str(e)}")
            raise



    async def batch_update_by_keys(
        self,
        db: AsyncSession,
        updates: List[Dict[str, Any]]
    ) -> List[SystemConfig]:
        """批量更新配置"""
        try:
            updated_configs = []

            for update_data in updates:
                key = update_data.get('key')
                value = update_data.get('value')

                if not key:
                    continue

                config = await self.get_by_key(db, key)
                if config:
                    config.config_value = str(value)
                    updated_configs.append(config)

            await db.commit()

            # 刷新所有对象
            for config in updated_configs:
                await db.refresh(config)

            # 清除缓存
            self.clear_cache()

            self.logger.info(f"批量更新配置成功，共{len(updated_configs)}个")
            return updated_configs
        except Exception as e:
            await db.rollback()
            self.logger.error(f"批量更新配置失败: {str(e)}")
            raise

    async def refresh_cache(self, db: AsyncSession):
        """刷新配置缓存（异步版本）"""
        try:
            # 清除现有缓存
            self.clear_cache()

            # 重新加载所有配置到缓存
            result = await db.execute(select(SystemConfig))
            configs = result.scalars().all()

            for config in configs:
                cache_key = f"config:{config.config_key}"
                self._config_cache[cache_key] = self._convert_config_value(config.config_value, config.config_type)

            self.logger.info(f"刷新配置缓存成功，共加载{len(configs)}个配置")
        except Exception as e:
            self.logger.error(f"刷新配置缓存失败: {str(e)}")
            raise

    def refresh_cache_sync(self, db: Session):
        """刷新配置缓存（同步版本）"""
        try:
            # 清除现有缓存
            self.clear_cache()

            # 重新加载所有配置到缓存
            result = db.execute(select(SystemConfig))
            configs = result.scalars().all()

            for config in configs:
                cache_key = f"config:{config.config_key}"
                self._config_cache[cache_key] = self._convert_config_value(config.config_value, config.config_type)

            self.logger.info(f"刷新配置缓存成功，共加载{len(configs)}个配置")
        except Exception as e:
            self.logger.error(f"刷新配置缓存失败: {str(e)}")
            raise


# 创建全局系统配置服务实例
system_config_service = SystemConfigService()
