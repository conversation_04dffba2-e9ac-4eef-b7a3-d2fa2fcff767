"""
用户编号生成器

提供用户编号自动生成功能，格式为 YY + 5位数字（如：YY00001, YY00002）
支持并发安全和唯一性保证
"""
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class UserNumberGenerator:
    """用户编号生成器"""
    
    PREFIX = "YY"
    NUMBER_LENGTH = 5
    MAX_RETRIES = 10
    
    @classmethod
    def generate_user_number_from_id(cls, user_id: int) -> str:
        """
        基于用户ID生成唯一的用户编号

        Args:
            user_id: 用户的数据库自增ID

        Returns:
            str: 生成的用户编号，格式为 YY + 5位数字
        """
        user_number = f"{cls.PREFIX}{user_id:0{cls.NUMBER_LENGTH}d}"
        logger.info(f"基于用户ID {user_id} 生成用户编号: {user_number}")
        return user_number
    

    
    @classmethod
    def validate_user_number(cls, user_number: str) -> bool:
        """
        验证用户编号格式是否正确
        
        Args:
            user_number: 要验证的用户编号
            
        Returns:
            bool: 格式是否正确
        """
        if not user_number:
            return False
            
        if not user_number.startswith(cls.PREFIX):
            return False
            
        if len(user_number) != len(cls.PREFIX) + cls.NUMBER_LENGTH:
            return False
            
        number_part = user_number[len(cls.PREFIX):]
        if not number_part.isdigit():
            return False
            
        return True
    
    @classmethod
    def extract_number_from_user_number(cls, user_number: str) -> Optional[int]:
        """
        从用户编号中提取数字部分
        
        Args:
            user_number: 用户编号
            
        Returns:
            Optional[int]: 数字部分，如果格式不正确则返回None
        """
        if not cls.validate_user_number(user_number):
            return None
            
        number_part = user_number[len(cls.PREFIX):]
        try:
            return int(number_part)
        except ValueError:
            return None


def generate_user_number_from_id(user_id: int) -> str:
    """
    基于用户ID生成唯一的用户编号（便捷函数）

    Args:
        user_id: 用户的数据库自增ID

    Returns:
        str: 生成的用户编号
    """
    return UserNumberGenerator.generate_user_number_from_id(user_id)


def validate_user_number(user_number: str) -> bool:
    """
    验证用户编号格式（便捷函数）
    
    Args:
        user_number: 要验证的用户编号
        
    Returns:
        bool: 格式是否正确
    """
    return UserNumberGenerator.validate_user_number(user_number)
