"""
UserService单元测试

测试用户服务的所有功能
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.user_service import UserService
from app.models.user import User, UserRole, UserGender
from app.schemas.user import UserCreate, UserUpdate
from app.utils.user_number_generator import validate_user_number
from unittest.mock import patch, Mock
from sqlalchemy.exc import IntegrityError


@pytest.mark.unit
class TestUserService:
    """用户服务测试类"""
    
    @pytest_asyncio.fixture
    async def user_service(self):
        """用户服务实例"""
        return UserService()
    
    @pytest.mark.asyncio
    async def test_create_user(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试创建用户"""
        # 准备测试数据
        user_data = test_data_factory.user_data(
            username="newuser",
            email="<EMAIL>"
        )
        
        # 创建用户
        user = await user_service.create(db_session, obj_in=user_data)
        
        # 验证结果
        assert user.id is not None
        assert user.username == "newuser"
        assert user.email == "<EMAIL>"
        assert user.nickname == "测试用户"
        assert user.role == UserRole.USER
        assert user.is_active is True
        assert user.is_verified is True
        assert user.password_hash is not None
        assert user.password_hash != "testpassword123"  # 密码应该被加密
    
    @pytest.mark.asyncio
    async def test_get_user_by_id(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试根据ID获取用户"""
        # 获取用户
        user = await user_service.get(db_session, test_user.id)
        
        # 验证结果
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username
        assert user.email == test_user.email
    
    @pytest.mark.asyncio
    async def test_get_user_by_username(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试根据用户名获取用户"""
        # 获取用户
        user = await user_service.get_by_username(db_session, test_user.username)
        
        # 验证结果
        assert user is not None
        assert user.id == test_user.id
        assert user.username == test_user.username
    
    @pytest.mark.asyncio
    async def test_get_user_by_email(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试根据邮箱获取用户"""
        # 获取用户
        user = await user_service.get_by_email(db_session, test_user.email)
        
        # 验证结果
        assert user is not None
        assert user.id == test_user.id
        assert user.email == test_user.email
    
    @pytest.mark.asyncio
    async def test_update_user(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试更新用户"""
        # 准备更新数据
        update_data = UserUpdate(
            nickname="更新后的昵称",
            avatar_url="https://example.com/new-avatar.jpg"
        )
        
        # 更新用户
        updated_user = await user_service.update(db_session, db_obj=test_user, obj_in=update_data)
        
        # 验证结果
        assert updated_user.id == test_user.id
        assert updated_user.nickname == "更新后的昵称"
        assert updated_user.avatar_url == "https://example.com/new-avatar.jpg"
        assert updated_user.username == test_user.username  # 其他字段不变
    
    @pytest.mark.asyncio
    async def test_authenticate_success(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试用户认证成功"""
        # 认证用户
        authenticated_user = await user_service.authenticate(
            db_session, test_user.username, "testpassword123"
        )
        
        # 验证结果
        assert authenticated_user is not None
        assert authenticated_user.id == test_user.id
        assert authenticated_user.username == test_user.username
    
    @pytest.mark.asyncio
    async def test_authenticate_wrong_password(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试用户认证失败（密码错误）"""
        # 认证用户
        authenticated_user = await user_service.authenticate(
            db_session, test_user.username, "wrongpassword"
        )
        
        # 验证结果
        assert authenticated_user is None
    
    @pytest.mark.asyncio
    async def test_authenticate_nonexistent_user(self, db_session: AsyncSession, user_service: UserService):
        """测试用户认证失败（用户不存在）"""
        # 认证用户
        authenticated_user = await user_service.authenticate(
            db_session, "nonexistent", "password"
        )
        
        # 验证结果
        assert authenticated_user is None
    
    @pytest.mark.asyncio
    async def test_wechat_login_new_user(self, db_session: AsyncSession, user_service: UserService):
        """测试微信登录（新用户）"""
        # 准备微信用户信息
        openid = "test_openid_123"
        user_info = {
            "nickname": "微信用户",
            "avatar_url": "https://example.com/avatar.jpg",
            "wechat_openid": openid,
            "wechat_unionid": "test_unionid_123"
        }
        
        # 微信登录
        user = await user_service.wechat_login(db_session, openid, user_info)
        
        # 验证结果
        assert user is not None
        assert user.wechat_openid == openid
        assert user.nickname == "微信用户"
        assert user.avatar_url == "https://example.com/avatar.jpg"
        assert user.is_verified is True
        assert user.last_login_at is not None
    
    @pytest.mark.asyncio
    async def test_wechat_login_existing_user(self, db_session: AsyncSession, user_service: UserService):
        """测试微信登录（已存在用户）"""
        # 先创建一个微信用户
        openid = "existing_openid_123"
        user_info = {
            "nickname": "原始昵称",
            "avatar_url": "https://example.com/old-avatar.jpg",
            "wechat_openid": openid
        }
        
        # 第一次登录
        first_login = await user_service.wechat_login(db_session, openid, user_info)
        first_login_time = first_login.last_login_at
        
        # 更新用户信息再次登录
        updated_info = {
            "nickname": "更新昵称",
            "avatar_url": "https://example.com/new-avatar.jpg",
            "wechat_openid": openid
        }
        
        # 第二次登录
        second_login = await user_service.wechat_login(db_session, openid, updated_info)
        
        # 验证结果
        assert second_login.id == first_login.id  # 同一个用户
        assert second_login.nickname == "更新昵称"  # 信息已更新
        assert second_login.avatar_url == "https://example.com/new-avatar.jpg"
        assert second_login.last_login_at > first_login_time  # 登录时间已更新
    
    @pytest.mark.asyncio
    async def test_search_users(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试搜索用户"""
        # 创建多个测试用户
        users_data = [
            test_data_factory.user_data(username="alice", nickname="爱丽丝", email="<EMAIL>"),
            test_data_factory.user_data(username="bob", nickname="鲍勃", email="<EMAIL>"),
            test_data_factory.user_data(username="charlie", nickname="查理", email="<EMAIL>"),
        ]
        
        for user_data in users_data:
            await user_service.create(db_session, obj_in=user_data)
        
        # 搜索用户
        users, total = await user_service.search_users(db_session, "alice", skip=0, limit=10)
        
        # 验证结果
        assert total >= 1
        assert len(users) >= 1
        assert any(user.username == "alice" for user in users)
    
    @pytest.mark.asyncio
    async def test_update_password(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试更新密码"""
        # 获取原始密码哈希
        original_hash = test_user.password_hash
        
        # 更新密码
        new_password_hash = "new_hashed_password"
        updated_user = await user_service.update_password(db_session, test_user.id, new_password_hash)
        
        # 验证结果
        assert updated_user.password_hash == new_password_hash
        assert updated_user.password_hash != original_hash
    
    @pytest.mark.asyncio
    async def test_verify_password(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试验证密码"""
        # 验证正确密码
        is_valid = await user_service.verify_password(db_session, test_user.id, "testpassword123")
        assert is_valid is True
        
        # 验证错误密码
        is_invalid = await user_service.verify_password(db_session, test_user.id, "wrongpassword")
        assert is_invalid is False
    
    @pytest.mark.asyncio
    async def test_get_multi_with_total(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试分页获取用户"""
        # 创建多个测试用户
        for i in range(5):
            user_data = test_data_factory.user_data(
                username=f"user{i}",
                email=f"user{i}@example.com"
            )
            await user_service.create(db_session, obj_in=user_data)
        
        # 分页获取用户
        users, total = await user_service.get_multi_with_total(db_session, skip=0, limit=3)
        
        # 验证结果
        assert total >= 5
        assert len(users) == 3

    @pytest.mark.asyncio
    async def test_create_user_with_new_fields(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试创建用户（包含新字段）"""
        # 准备测试数据
        user_data = test_data_factory.user_data(
            username="newuser_with_profile",
            email="<EMAIL>",
            location="北京市",
            user_number="USER001",
            gender=UserGender.MALE,
            age=25
        )

        # 创建用户
        user = await user_service.create(db_session, obj_in=user_data)

        # 验证结果
        assert user.id is not None
        assert user.username == "newuser_with_profile"
        assert user.location == "北京市"
        assert user.user_number == "USER001"
        assert user.gender == UserGender.MALE.value
        assert user.age == 25

    @pytest.mark.asyncio
    async def test_update_user_with_new_fields(self, db_session: AsyncSession, user_service: UserService, test_user: User):
        """测试更新用户（包含新字段）"""
        # 准备更新数据
        update_data = UserUpdate(
            nickname="更新后的昵称",
            location="上海市",
            user_number="USER002",
            gender=UserGender.FEMALE,
            age=30
        )

        # 更新用户
        updated_user = await user_service.update(db_session, db_obj=test_user, obj_in=update_data)

        # 验证结果
        assert updated_user.id == test_user.id
        assert updated_user.nickname == "更新后的昵称"
        assert updated_user.location == "上海市"
        assert updated_user.user_number == "USER002"
        assert updated_user.gender == UserGender.FEMALE.value
        assert updated_user.age == 30

    @pytest.mark.asyncio
    async def test_user_number_uniqueness(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试用户编号唯一性"""
        # 创建第一个用户
        user_data1 = test_data_factory.user_data(
            username="user1",
            email="<EMAIL>",
            user_number="UNIQUE001"
        )
        await user_service.create(db_session, obj_in=user_data1)

        # 尝试创建具有相同用户编号的第二个用户
        user_data2 = test_data_factory.user_data(
            username="user2",
            email="<EMAIL>",
            user_number="UNIQUE001"
        )

        # 应该抛出异常
        with pytest.raises(Exception):  # 具体异常类型取决于数据库实现
            await user_service.create(db_session, obj_in=user_data2)

    @pytest.mark.asyncio
    async def test_age_validation(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试年龄验证"""
        # 测试有效年龄
        user_data_valid = test_data_factory.user_data(
            username="valid_age_user",
            email="<EMAIL>",
            age=25
        )
        user = await user_service.create(db_session, obj_in=user_data_valid)
        assert user.age == 25

        # 测试边界值
        user_data_boundary = test_data_factory.user_data(
            username="boundary_age_user",
            email="<EMAIL>",
            age=150
        )
        user_boundary = await user_service.create(db_session, obj_in=user_data_boundary)
        assert user_boundary.age == 150

    @pytest.mark.asyncio
    async def test_create_user_with_auto_generated_user_number(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试创建用户时自动生成用户编号"""
        # 准备测试数据（不包含用户编号）
        user_data = test_data_factory.user_data(
            username="user_with_auto_number",
            email="<EMAIL>"
        )

        # 创建用户
        user = await user_service.create(db_session, obj_in=user_data)

        # 验证用户编号已自动生成
        assert user.user_number is not None
        assert validate_user_number(user.user_number)
        assert user.user_number.startswith("YY")
        assert len(user.user_number) == 7  # YY + 5位数字

    @pytest.mark.asyncio
    async def test_create_user_with_provided_user_number(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试创建用户时提供自定义用户编号"""
        # 准备测试数据（包含自定义用户编号）
        custom_number = "YY88888"
        user_data = test_data_factory.user_data(
            username="user_with_custom_number",
            email="<EMAIL>",
            user_number=custom_number
        )

        # 创建用户
        user = await user_service.create(db_session, obj_in=user_data)

        # 验证使用了提供的用户编号
        assert user.user_number == custom_number

    @pytest.mark.asyncio
    async def test_create_multiple_users_unique_numbers(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试创建多个用户时编号的唯一性"""
        user_numbers = set()

        # 创建多个用户
        for i in range(5):
            user_data = test_data_factory.user_data(
                username=f"user_{i}",
                email=f"user_{i}@example.com"
            )

            user = await user_service.create(db_session, obj_in=user_data)

            # 验证用户编号格式正确且唯一
            assert user.user_number is not None
            assert validate_user_number(user.user_number)
            assert user.user_number not in user_numbers
            user_numbers.add(user.user_number)

        # 验证所有编号都不相同
        assert len(user_numbers) == 5

    @pytest.mark.asyncio
    async def test_wechat_login_new_user_auto_number(self, db_session: AsyncSession, user_service: UserService):
        """测试微信登录新用户时自动生成用户编号"""
        # 准备微信用户信息
        openid = "test_openid_auto_number"
        user_info = {
            "nickname": "微信用户自动编号",
            "avatar_url": "https://example.com/avatar.jpg",
            "wechat_openid": openid,
            "wechat_unionid": "test_unionid_auto"
        }

        # 微信登录（新用户）
        user = await user_service.wechat_login(db_session, openid, user_info)

        # 验证用户编号已自动生成
        assert user is not None
        assert user.user_number is not None
        assert validate_user_number(user.user_number)
        assert user.wechat_openid == openid
        assert user.nickname == "微信用户自动编号"

    @pytest.mark.asyncio
    async def test_user_number_generation_with_retry_on_conflict(self, db_session: AsyncSession, user_service: UserService, test_data_factory):
        """测试用户编号冲突时的重试机制"""
        # 模拟编号生成冲突的情况
        with patch('app.utils.user_number_generator.generate_user_number') as mock_generate:
            # 第一次调用抛出IntegrityError，第二次成功
            mock_generate.side_effect = [
                IntegrityError("duplicate key", "orig", "params"),
                "YY00123"
            ]

            user_data = test_data_factory.user_data(
                username="retry_user",
                email="<EMAIL>"
            )

            # 由于我们的重试逻辑在服务层，这个测试需要调整
            # 实际上IntegrityError会在数据库层面处理
            user = await user_service.create(db_session, obj_in=user_data)

            # 验证用户创建成功
            assert user is not None
            assert user.user_number is not None
