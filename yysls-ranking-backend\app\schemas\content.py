"""
内容管理相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, ConfigDict


class ContentBase(BaseModel):
    """内容基础模型 - 按照新的MySQL表结构"""
    content_type: str = Field(..., description="内容类型", max_length=50)
    title: str = Field(..., description="标题", max_length=200)
    content: Optional[str] = Field(None, description="内容正文")
    is_published: bool = Field(False, description="发布状态")
    publish_at: Optional[datetime] = Field(None, description="发布时间")


class ContentCreate(ContentBase):
    """创建内容的请求模型"""
    pass


class ContentUpdate(BaseModel):
    """更新内容的请求模型"""
    title: Optional[str] = Field(None, description="标题", max_length=200)
    content: Optional[str] = Field(None, description="内容")
    content_type: Optional[str] = Field(None, description="内容类型", max_length=50)
    summary: Optional[str] = Field(None, description="摘要")
    keywords: Optional[str] = Field(None, description="关键词", max_length=200)
    status: Optional[str] = Field(None, description="状态", max_length=20)
    is_featured: Optional[bool] = Field(None, description="是否精选")
    is_top: Optional[bool] = Field(None, description="是否置顶")
    display_order: Optional[int] = Field(None, description="显示顺序")
    publish_time: Optional[datetime] = Field(None, description="发布时间")
    expire_time: Optional[datetime] = Field(None, description="过期时间")


class ContentResponse(ContentBase):
    """内容响应模型"""
    id: int = Field(..., description="内容ID")
    view_count: int = Field(0, description="浏览次数")
    created_by: Optional[int] = Field(None, description="创建者ID")
    updated_by: Optional[int] = Field(None, description="更新者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class BroadcastMessageBase(BaseModel):
    """播报消息基础模型"""
    title: str = Field(..., description="标题", max_length=200)
    content: str = Field(..., description="内容")
    message_type: str = Field(..., description="消息类型", max_length=50)
    priority: int = Field(0, description="优先级")
    is_active: bool = Field(True, description="是否启用")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    display_duration: int = Field(5, description="显示时长（秒）")
    target_audience: Optional[str] = Field(None, description="目标受众", max_length=100)


class BroadcastMessageCreate(BroadcastMessageBase):
    """创建播报消息的请求模型"""
    pass


class BroadcastMessageUpdate(BaseModel):
    """更新播报消息的请求模型"""
    title: Optional[str] = Field(None, description="标题", max_length=200)
    content: Optional[str] = Field(None, description="内容")
    message_type: Optional[str] = Field(None, description="消息类型", max_length=50)
    priority: Optional[int] = Field(None, description="优先级")
    is_active: Optional[bool] = Field(None, description="是否启用")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    display_duration: Optional[int] = Field(None, description="显示时长（秒）")
    target_audience: Optional[str] = Field(None, description="目标受众", max_length=100)


class BroadcastMessageResponse(BroadcastMessageBase):
    """播报消息响应模型"""
    id: int = Field(..., description="消息ID")
    created_by: Optional[int] = Field(None, description="创建者ID")
    updated_by: Optional[int] = Field(None, description="更新者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)
