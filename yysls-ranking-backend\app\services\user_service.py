"""
用户业务服务

处理用户相关的业务逻辑，包括：
- 用户注册和登录
- 微信登录集成
- 用户信息管理
- 权限验证
- 积分管理
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, func
from datetime import datetime

from app.services.base import BaseService
from app.models.user import User, UserRole
from app.schemas.user import UserCreate, UserUpdate
from app.utils.security import get_password_hash, verify_password



class UserService(BaseService[User, UserCreate, UserUpdate]):
    """用户服务类"""
    
    def __init__(self):
        super().__init__(User)
    
    def get_by_username(self, db: Session, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            result = db.execute(
                select(User).where(User.username == username)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"根据用户名获取用户失败 username={username}: {str(e)}")
            raise
    

    
    def get_by_wechat_openid(self, db: Session, openid: str) -> Optional[User]:
        """根据微信OpenID获取用户"""
        try:
            result = db.execute(
                select(User).where(User.wechat_openid == openid)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"根据微信OpenID获取用户失败 openid={openid}: {str(e)}")
            raise
    
    def create_user(self, db: Session, user_in: UserCreate) -> User:
        """创建新用户"""
        try:
            # 检查用户名是否已存在
            if self.get_by_username(db, user_in.username):
                raise ValueError(f"用户名 '{user_in.username}' 已存在")

            # 创建用户对象
            user_data = user_in.model_dump()
            if 'password' in user_data:
                user_data['password_hash'] = get_password_hash(user_data.pop('password'))

            # 如果没有提供用户编号，先不设置，创建后基于ID生成
            if not user_data.get('user_number'):
                user_data.pop('user_number', None)  # 移除空的user_number字段

            db_user = User(**user_data)
            db.add(db_user)
            db.commit()
            db.refresh(db_user)

            # 如果用户编号为空，基于用户ID生成编号
            if not db_user.user_number:
                from app.utils.user_number_generator import generate_user_number_from_id
                db_user.user_number = generate_user_number_from_id(db_user.id)
                db.commit()
                db.refresh(db_user)

            self.logger.info(f"创建用户成功 username={user_in.username}, user_number={db_user.user_number}")
            return db_user

        except Exception as e:
            db.rollback()
            self.logger.error(f"创建用户失败: {str(e)}")
            raise
    
    def authenticate(self, db: Session, username: str, password: str) -> Optional[User]:
        """用户认证"""
        try:
            user = self.get_by_username(db, username)
            if not user:
                return None

            if not verify_password(password, user.password_hash):
                return None

            # 更新最后登录时间
            user.last_login_at = datetime.utcnow()
            db.commit()

            self.logger.info(f"用户认证成功 username={username}")
            return user
        except Exception as e:
            self.logger.error(f"用户认证失败 username={username}: {str(e)}")
            raise
    
    async def wechat_login(self, db: Session, openid: str, user_info: Dict[str, Any]) -> User:
        """微信登录"""
        try:
            # 查找现有用户
            user = self.get_by_wechat_openid(db, openid)
            
            if user:
                # 更新用户信息和最后登录时间
                if user_info.get('nickname'):
                    user.nickname = user_info['nickname']
                if user_info.get('avatar_url'):
                    user.avatar_url = user_info['avatar_url']
                user.last_login_at = datetime.utcnow()
                
                db.commit()
                db.refresh(user)
                
                self.logger.info(f"微信用户登录成功 openid={openid}")
                return user
            else:
                # 创建新用户
                username = f"wx_{openid[:10]}"  # 生成唯一用户名
                user_data = {
                    'username': username,
                    'nickname': user_info.get('nickname', username),
                    'avatar_url': user_info.get('avatar_url'),
                    'wechat_openid': openid,
                    'wechat_unionid': user_info.get('unionid'),
                    'is_verified': True,  # 微信用户默认已验证
                    'last_login_at': datetime.utcnow()
                }

                db_user = User(**user_data)
                db.add(db_user)
                db.commit()
                db.refresh(db_user)

                # 基于用户ID生成编号
                from app.utils.user_number_generator import generate_user_number_from_id
                db_user.user_number = generate_user_number_from_id(db_user.id)
                db.commit()
                db.refresh(db_user)

                self.logger.info(f"微信新用户注册成功 openid={openid}, user_number={db_user.user_number}")
                return db_user
        except Exception as e:
            db.rollback()
            self.logger.error(f"微信登录失败 openid={openid}: {str(e)}")
            raise
    
    async def update_points(self, db: Session, user_id: int, points_change: int) -> User:
        """更新用户积分"""
        try:
            user = self.get(db, user_id)
            if not user:
                raise ValueError(f"用户不存在 ID={user_id}")
            
            user.points += points_change
            if user.points < 0:
                user.points = 0
            
            db.commit()
            db.refresh(user)
            
            self.logger.info(f"更新用户积分成功 user_id={user_id}, change={points_change}, total={user.points}")
            return user
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新用户积分失败 user_id={user_id}: {str(e)}")
            raise
    
    async def get_active_users(self, db: Session, skip: int = 0, limit: int = 100) -> List[User]:
        """获取活跃用户列表"""
        return self.get_multi(
            db, 
            skip=skip, 
            limit=limit, 
            filters={'is_active': True},
            order_by='last_login_at'
        )
    
    async def get_admins(self, db: Session) -> List[User]:
        """获取管理员列表（包括超级管理员）"""
        admin_users = self.get_multi(
            db,
            filters={'role': UserRole.ADMIN, 'is_active': True}
        )
        super_admin_users = self.get_multi(
            db,
            filters={'role': UserRole.SUPER_ADMIN, 'is_active': True}
        )
        return admin_users + super_admin_users
    
    async def is_admin(self, db: Session, user_id: int) -> bool:
        """检查用户是否为管理员（包括超级管理员）"""
        try:
            user = self.get(db, user_id)
            return user and user.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN] and user.is_active
        except Exception as e:
            self.logger.error(f"检查管理员权限失败 user_id={user_id}: {str(e)}")
            return False
    
    async def deactivate_user(self, db: Session, user_id: int) -> bool:
        """停用用户"""
        try:
            user = self.get(db, user_id)
            if not user:
                return False
            
            user.is_active = False
            db.commit()
            
            self.logger.info(f"停用用户成功 user_id={user_id}")
            return True
        except Exception as e:
            db.rollback()
            self.logger.error(f"停用用户失败 user_id={user_id}: {str(e)}")
            raise
    
    def search_users(
        self,
        db: Session,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        **filters
    ) -> tuple[List[User], int]:
        """搜索用户（返回结果和总数）"""
        try:
            # 构建搜索条件
            search_conditions = or_(
                User.username.ilike(f"%{search_term}%"),
                User.nickname.ilike(f"%{search_term}%")
            )

            # 构建过滤条件
            filter_conditions = self._build_filters(**filters)

            # 组合所有条件
            all_conditions = [search_conditions]
            if filter_conditions:
                all_conditions.extend(filter_conditions)

            # 构建查询
            query = select(User).where(and_(*all_conditions))
            count_query = select(func.count(User.id)).where(and_(*all_conditions))

            # 获取总数
            total_result = db.execute(count_query)
            total = total_result.scalar()

            # 获取数据
            query = query.offset(skip).limit(limit)
            result = db.execute(query)
            users = list(result.scalars().all())

            return users, total
        except Exception as e:
            self.logger.error(f"搜索用户失败 search_term={search_term}: {str(e)}")
            raise



    async def update_password(self, db: Session, user_id: int, hashed_password: str) -> User:
        """更新用户密码"""
        try:
            user = self.get(db, user_id)
            if not user:
                raise ValueError(f"用户不存在 ID={user_id}")

            user.password_hash = hashed_password
            db.commit()
            db.refresh(user)

            self.logger.info(f"更新用户密码成功 user_id={user_id}")
            return user
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新用户密码失败 user_id={user_id}: {str(e)}")
            raise

    async def verify_password(self, db: Session, user_id: int, password: str) -> bool:
        """验证用户密码"""
        try:
            user = self.get(db, user_id)
            if not user or not user.password_hash:
                return False

            return verify_password(password, user.password_hash)
        except Exception as e:
            self.logger.error(f"验证用户密码失败 user_id={user_id}: {str(e)}")
            return False


# 创建全局用户服务实例
user_service = UserService()
