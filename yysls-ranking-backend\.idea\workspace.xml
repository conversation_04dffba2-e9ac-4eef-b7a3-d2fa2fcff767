<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db8b5f25-d3f2-4255-b8a3-6d475ece52d5" name="Changes" comment="refactor(api): 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由">
      <change afterPath="$PROJECT_DIR$/API_DEVELOPMENT_SUMMARY.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/DEPLOYMENT.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Excel模板使用说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MILESTONES.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/RANKING_TYPE_NAME_MODIFICATION.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/TESTING.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/deploy-fix.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/deploy-secure.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/API_COMPLETE_DOCUMENTATION.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/EXCEL_INTEGRATION_API.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/solutions/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/solutions/USAGE_EXAMPLES.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/solutions/database_session_management_fix.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fix-docker-error.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fix-lower-case-table-names.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fix-mysql-error1.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fix-mysql-restart.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/generate_excel_template.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/install_excel_deps.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/app.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/nginx/conf.d/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pytest.ini" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/recreate_db.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/run_tests.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/generate-ssl.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/generate_postman_collection.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/init_admin.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/nginx-config.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/quick_fix_session.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/setup-nginx.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/test_admin_login.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/test_api_endpoints.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/verify_database_structure.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts/verify_session_consistency.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/add_team_name_field.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/fix_ranking_details_structure.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/start-services.sh" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/start.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_api.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_api_endpoints.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_app_startup.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_auth_simple.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_excel_functionality.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_fixed_login.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_miniprogram_login.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_ranking_api_response.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_ranking_type_name.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/uploads/temp/excel/a80ca6e93c7947f0a6f361bc43c5bcdf_1.xlsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/榜单明细模板.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/models/user.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/models/user.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/schemas/user.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/schemas/user.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/services/user_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/services/user_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/DATABASE_SQL.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/DATABASE_SQL.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/database_design.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/database_design.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nginx/conf.d/yysls.conf.template" beforeDir="false" afterPath="$PROJECT_DIR$/nginx/conf.d/yysls.conf.template" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/generate_api_docs.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/generate_api_docs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/generate_sql_scripts.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/generate_sql_scripts.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/mysql_create_tables.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/mysql_create_tables.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/test_api/test_upload_excel.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/test_api/test_upload_excel.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/test_services/test_user_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/test_services/test_user_service.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../go/server/go1.22.2" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30YMmWunNTDkWlTPetish0WzfN0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.automatic.dependencies.download&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/h5_code/yysls/yysls-ranking-backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;http.proxy&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\h5_code\yysls\yysls-ranking-backend\ssh" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="refactor: 优化 MySQL配置文件设置&#10;&#10;- 移除了不必要的注释和冗余配置&#10;- 调整了连接配置和缓冲区大小，以适应更高负载&#10;- 更新了字符集和排序规则配置&#10;-简化了日志和安全配置&#10;- 添加了 binlog 配置，用于数据复制和恢复- 设置了更严格的 SQL 模式" />
    <MESSAGE value="build(deps): 添加 email-validator以支持 Pydantic EmailStr- 在 requirements.txt 中添加 email-validator==2.1.0&#10;- 此依赖用于支持 Pydantic 库中的 EmailStr 类型验证" />
    <MESSAGE value="feat: 重构赞助商相关功能&#10;&#10;- 简化了赞助商模型，移除了不必要的字段&#10;- 重新设计了赞助商列表和搜索逻辑，支持分页和排序&#10;- 添加了更新赞助商排序顺序的功能&#10;- 修改了激活状态切换逻辑&#10;-移除了异步数据库操作，改为同步操作&#10;- 更新了相关的数据库设计文档和SQL脚本" />
    <MESSAGE value="feat: 优化用户模型和接口&#10;&#10;- 在 User 模型中添加了年龄、性别、所在地等新字段&#10;- 更新了用户相关的 API 接口，支持新增的字段&#10;- 优化了用户服务中的部分方法，提高了代码可读性和性能&#10;- 修复了一些与用户相关的潜在安全问题" />
    <MESSAGE value="feat: 重构日志系统并初始化全局日志配置" />
    <MESSAGE value="feat(app): 添加 Excel 文件处理工具类&#10;&#10;- 实现了 Excel 文件验证、解析和模板生成功能- 支持榜单明细 Excel 文件的导入和导出&#10;- 提供了数据验证和错误处理机制" />
    <MESSAGE value="feat: 简化赞助商模型并添加用户个人资料字段&#10;&#10;- 移除赞助商模型中的冗余字段，只保留名称和logo_url&#10;- 在用户模型中添加地点、用户编号、性别和年龄字段&#10;- 新增文件上传API，支持Excel文件验证、解析和临时上传&#10;- 添加相关单元测试用例" />
    <MESSAGE value="feat: 支持 Excel 文件导入榜单明细&#10;&#10;- 新增创建和更新榜单时导入 Excel 数据的功能&#10;- 添加解析 Excel 文件的工具类 ExcelHandler&#10;- 修改数据库模型以支持榜单明细数据&#10;- 优化 API 接口，增加 Excel 文件上传和处理相关参数" />
    <MESSAGE value="feat: 启用系统配置和内容管理模块&#10;&#10;- 移除系统配置和服务模块的注释&#10;- 更新数据库会话类型从 AsyncSession 到 Session&#10;-修正部分导入语句和类型注解&#10;- 优化部分代码结构以适应同步数据库操作" />
    <MESSAGE value="fix: 优化榜单更新接口并添加队伍名称字段&#10;&#10;- 移除 update_ranking 接口中的 ranking_id 参数，使用请求体中的 id&#10;- 在 RankingDetail 模型中添加 team_name 字段&#10;- 更新 RankingUpdate 模型，将 id 设为必填字段" />
    <MESSAGE value="docs: 添加升级数据库的说明&#10;&#10;- 新增 alembic.md 文件，说明如何使用 Alembic 进行数据库升级&#10;- 提供升级到最新版本和特定版本的命令示例" />
    <MESSAGE value="feat: 更新 ranking_details 表结构&#10;&#10;- 在 ranking_details 表中添加 team_name 字段&#10;- 确保 ranking_details 表结构与当前模型一致，包括 rank_start、rank_end、completion_time、participant_count 和 team_info 字段- 添加相关索引以优化查询性能" />
    <MESSAGE value="feat: 更新榜单明细功能并优化相关模块&#10;&#10;- 在 ranking_service 中添加 update_ranking_detail 方法，实现榜单明细更新功能&#10;- 在 ranking.py 中为 RankingCreate 和 RankingUpdate 模型添加 team_name 字段&#10;- 在 excel_handler.py 中更新 Excel 导入导出逻辑，支持队伍名称字段&#10;- 优化 rankings.py 中的响应模型转换方法，使用 model_validate 替代 from_orm" />
    <MESSAGE value="feat: 新增内容管理和系统配置模型" />
    <MESSAGE value="feat: 为榜单类型添加中文名称显示" />
    <MESSAGE value="feat(auth): 支持微信小程序登录" />
    <MESSAGE value="feat: 为未授权用户设置默认头像" />
    <MESSAGE value="refactor: 修改用户性别枚举值及约束" />
    <MESSAGE value="feat(auth): 扩展管理员权限包括超级管理员&#10;&#10;- 修改了多个文件中的权限检查逻辑，允许超级管理员拥有与管理员相同的权限&#10;- 更新了相关函数和API端点的注释，明确支持管理员和超级管理员&#10;- 调整了获取管理员列表和检查管理员权限的逻辑，包含超级管理员在内" />
    <MESSAGE value="fix: 重构系统配置相关代码&#10;&#10;- 修改了系统配置的模型、schema和服务层逻辑" />
    <MESSAGE value="fix: 更新系统配置接口" />
    <MESSAGE value="fix: 优化配置缓存刷新逻辑" />
    <MESSAGE value="fix: 修改用户更新接口并优化用户模型" />
    <MESSAGE value="refactor(api): 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由" />
    <MESSAGE value="feat: 优化用户编号生成逻辑&#10;&#10;- 引入新的用户编号验证函数，确保编号格式正确&#10;- 新增用户编号生成逻辑，基于用户ID生成唯一编号- 在用户创建和微信注册流程中集成用户编号生成" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 优化用户编号生成逻辑&#10;&#10;- 引入新的用户编号验证函数，确保编号格式正确&#10;- 新增用户编号生成逻辑，基于用户ID生成唯一编号- 在用户创建和微信注册流程中集成用户编号生成" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>